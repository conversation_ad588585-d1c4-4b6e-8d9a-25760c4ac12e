/* Main App container styling */
.App {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Heading styles */
.App h1 {
  color: #333;
  margin-bottom: 30px;
  font-size: 2.5rem;
  font-weight: 600;
}

/* Button styling */
.App button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 12px 24px;
  font-size: 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  font-weight: 500;
}

/* Button hover effect */
.App button:hover {
  background-color: #0056b3;
}

/* Button active/pressed effect */
.App button:active {
  background-color: #004085;
  transform: translateY(1px);
}

/* Button focus effect for accessibility */
.App button:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
  .App {
    padding: 15px;
  }
  
  .App h1 {
    font-size: 2rem;
    margin-bottom: 20px;
  }
  
  .App button {
    padding: 10px 20px;
    font-size: 14px;
  }
}
