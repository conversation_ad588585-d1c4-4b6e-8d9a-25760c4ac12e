# n8n Friendly UI Example

A simple React + Node.js app to trigger n8n workflows via a friendly UI.

## Setup

   Start n8n:
   ```bash
   docker-compose up -d

2.Start backend:

Bash 
cd backend
npm install
npm start

3.Start frontend:

bash
cd frontend
npm install
npm start

4.Open http://localhost:3000 and click "Trigger Workflow"

---

### 📤 Step 7: Push to GitHub

Now initialize Git and push to your GitHub account:

```bash
cd ..
git init
git add .
git commit -m "Initial commit: n8n friendly UI example"

Then go to GitHub and create a new repo called n8n-friendly-ui-example.
Then push:

Bash 
git remote add origin https://github.com/raj3176/n8n-friendly-ui-example.git
git branch -M main
git push -u origin main
