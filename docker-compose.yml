# Define the version of the Docker Compose file format
version: '3.8'

# Define the services to run
services:
  # Define the n8n service
  n8n:
    # Use the official n8n Docker image
    image: n8nio/n8n

    # Map port 5678 on the host to port 5678 in the container
    ports:
      - "5678:5678"

    # Set environment variables for n8n
    environment:
      # Enable basic authentication
      - N8N_BASIC_AUTH_ACTIVE=true

      # Set the basic auth username
      - N8N_BASIC_AUTH_USER=admin

      # Set the basic auth password
      - N8N_BASIC_AUTH_PASSWORD=password

    # Mount a volume to persist n8n data
    volumes:
      - n8n_data:/home/<USER>/.n8n

# Define a named volume to persist data
volumes:
  n8n_data:
