{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Rajeev\\\\Code\\\\N8N-UI\\\\frontend\\\\src\\\\app.js\";\n// Import React to create the component\nimport React from 'react';\n\n// Import Axios to make HTTP requests to the backend\nimport axios from 'axios';\n\n// Import the CSS file for styling\nimport './App.css';\n\n// Define the main App component\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  // Define a function to trigger the workflow\n  const triggerWorkflow = async () => {\n    try {\n      // Send a POST request to the backend to trigger the workflow\n      await axios.post('http://localhost:3001/api/workflows/1/invoke');\n\n      // Show a success alert to the user\n      alert('Workflow triggered!');\n    } catch (error) {\n      // Show an error alert if something goes wrong\n      alert('Error triggering workflow');\n    }\n  };\n\n  // Return the JSX to render the UI\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"n8n Friendly UI\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: triggerWorkflow,\n      children: \"Trigger Workflow\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n}\n\n// Export the App component to be used in index.js\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "axios", "jsxDEV", "_jsxDEV", "App", "triggerWorkflow", "post", "alert", "error", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Rajeev/Code/N8N-UI/frontend/src/app.js"], "sourcesContent": ["// Import React to create the component\r\nimport React from 'react';\r\n\r\n// Import Axios to make HTTP requests to the backend\r\nimport axios from 'axios';\r\n\r\n// Import the CSS file for styling\r\nimport './App.css';\r\n\r\n// Define the main App component\r\nfunction App() {\r\n  // Define a function to trigger the workflow\r\n  const triggerWorkflow = async () => {\r\n    try {\r\n      // Send a POST request to the backend to trigger the workflow\r\n      await axios.post('http://localhost:3001/api/workflows/1/invoke');\r\n\r\n      // Show a success alert to the user\r\n      alert('Workflow triggered!');\r\n    } catch (error) {\r\n      // Show an error alert if something goes wrong\r\n      alert('Error triggering workflow');\r\n    }\r\n  };\r\n\r\n  // Return the JSX to render the UI\r\n  return (\r\n    <div className=\"App\">\r\n      {/* Display a heading */}\r\n      <h1>n8n Friendly UI</h1>\r\n\r\n      {/* But<PERSON> to trigger the workflow */}\r\n      <button onClick={triggerWorkflow}>Trigger Workflow</button>\r\n    </div>\r\n  );\r\n}\r\n\r\n// Export the App component to be used in index.js\r\nexport default App;"], "mappings": ";AAAA;AACA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,OAAOC,KAAK,MAAM,OAAO;;AAEzB;AACA,OAAO,WAAW;;AAElB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,SAASC,GAAGA,CAAA,EAAG;EACb;EACA,MAAMC,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF;MACA,MAAMJ,KAAK,CAACK,IAAI,CAAC,8CAA8C,CAAC;;MAEhE;MACAC,KAAK,CAAC,qBAAqB,CAAC;IAC9B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd;MACAD,KAAK,CAAC,2BAA2B,CAAC;IACpC;EACF,CAAC;;EAED;EACA,oBACEJ,OAAA;IAAKM,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAElBP,OAAA;MAAAO,QAAA,EAAI;IAAe;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAGxBX,OAAA;MAAQY,OAAO,EAAEV,eAAgB;MAAAK,QAAA,EAAC;IAAgB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACxD,CAAC;AAEV;;AAEA;AAAAE,EAAA,GA3BSZ,GAAG;AA4BZ,eAAeA,GAAG;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}