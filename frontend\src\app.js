// Import React to create the component
import React from 'react';

// Import Axios to make HTTP requests to the backend
import axios from 'axios';

// Import the CSS file for styling
import './App.css';

// Define the main App component
function App() {
  // Define a function to trigger the workflow
  const triggerWorkflow = async () => {
    try {
      // Send a POST request to the backend to trigger the workflow
      const response = await axios.post('http://localhost:3001/api/workflows/1/invoke');

      // Show a success alert with the response from N8N
      alert(`Success! ${response.data.message}`);
    } catch (error) {
      // Show an error alert if something goes wrong
      console.error('Error details:', error.response?.data);
      alert(`Error: ${error.response?.data?.details || 'Failed to trigger workflow'}`);
    }
  };

  // Return the JSX to render the UI
  return (
    <div className="App">
      {/* Display a heading */}
      <h1>n8n Friendly UI</h1>

      {/* <PERSON><PERSON> to trigger the workflow */}
      <button onClick={triggerWorkflow}>Trigger Workflow</button>
    </div>
  );
}

// Export the App component to be used in index.js
export default App;