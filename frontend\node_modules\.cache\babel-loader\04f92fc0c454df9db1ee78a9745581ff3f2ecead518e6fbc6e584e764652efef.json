{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Rajeev\\\\Code\\\\N8N-UI\\\\frontend\\\\src\\\\index.js\";\n// Import React to enable JSX\nimport React from 'react';\n\n// Import ReactDOM to render the app to the DOM\nimport ReactD<PERSON> from 'react-dom/client';\n\n// Import the main App component\nimport App from './app';\n\n// Create a root element and render the App component\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst root = ReactDOM.createRoot(document.getElementById('root'));\n\n// Render the App component inside the root element\nroot.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 15,\n  columnNumber: 3\n}, this));", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "jsxDEV", "_jsxDEV", "root", "createRoot", "document", "getElementById", "render", "StrictMode", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Rajeev/Code/N8N-UI/frontend/src/index.js"], "sourcesContent": ["// Import React to enable JSX\r\nimport React from 'react';\r\n\r\n// Import ReactDOM to render the app to the DOM\r\nimport ReactDOM from 'react-dom/client';\r\n\r\n// Import the main App component\r\nimport App from './app';\r\n\r\n// Create a root element and render the App component\r\nconst root = ReactDOM.createRoot(document.getElementById('root'));\r\n\r\n// Render the App component inside the root element\r\nroot.render(\r\n  <React.StrictMode>\r\n    <App />\r\n  </React.StrictMode>\r\n);\r\n"], "mappings": ";AAAA;AACA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,OAAOC,QAAQ,MAAM,kBAAkB;;AAEvC;AACA,OAAOC,GAAG,MAAM,OAAO;;AAEvB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,IAAI,GAAGJ,QAAQ,CAACK,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC;;AAEjE;AACAH,IAAI,CAACI,MAAM,cACTL,OAAA,CAACJ,KAAK,CAACU,UAAU;EAAAC,QAAA,eACfP,OAAA,CAACF,GAAG;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACS,CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}