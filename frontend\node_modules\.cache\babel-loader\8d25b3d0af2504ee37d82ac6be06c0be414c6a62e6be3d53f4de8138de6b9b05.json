{"ast": null, "code": "'use strict';\n\nvar globalThis = require('../internals/global-this');\nvar shared = require('../internals/shared');\nvar hasOwn = require('../internals/has-own-property');\nvar uid = require('../internals/uid');\nvar NATIVE_SYMBOL = require('../internals/symbol-constructor-detection');\nvar USE_SYMBOL_AS_UID = require('../internals/use-symbol-as-uid');\nvar Symbol = globalThis.Symbol;\nvar WellKnownSymbolsStore = shared('wks');\nvar createWellKnownSymbol = USE_SYMBOL_AS_UID ? Symbol['for'] || Symbol : Symbol && Symbol.withoutSetter || uid;\nmodule.exports = function (name) {\n  if (!hasOwn(WellKnownSymbolsStore, name)) {\n    WellKnownSymbolsStore[name] = NATIVE_SYMBOL && hasOwn(Symbol, name) ? Symbol[name] : createWellKnownSymbol('Symbol.' + name);\n  }\n  return WellKnownSymbolsStore[name];\n};", "map": {"version": 3, "names": ["globalThis", "require", "shared", "hasOwn", "uid", "NATIVE_SYMBOL", "USE_SYMBOL_AS_UID", "Symbol", "WellKnownSymbolsStore", "createWellKnownSymbol", "withoutSetter", "module", "exports", "name"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Rajeev/Code/N8N-UI/frontend/node_modules/core-js-pure/internals/well-known-symbol.js"], "sourcesContent": ["'use strict';\nvar globalThis = require('../internals/global-this');\nvar shared = require('../internals/shared');\nvar hasOwn = require('../internals/has-own-property');\nvar uid = require('../internals/uid');\nvar NATIVE_SYMBOL = require('../internals/symbol-constructor-detection');\nvar USE_SYMBOL_AS_UID = require('../internals/use-symbol-as-uid');\n\nvar Symbol = globalThis.Symbol;\nvar WellKnownSymbolsStore = shared('wks');\nvar createWellKnownSymbol = USE_SYMBOL_AS_UID ? Symbol['for'] || Symbol : Symbol && Symbol.withoutSetter || uid;\n\nmodule.exports = function (name) {\n  if (!hasOwn(WellKnownSymbolsStore, name)) {\n    WellKnownSymbolsStore[name] = NATIVE_SYMBOL && hasOwn(Symbol, name)\n      ? Symbol[name]\n      : createWellKnownSymbol('Symbol.' + name);\n  } return WellKnownSymbolsStore[name];\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,UAAU,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AACpD,IAAIC,MAAM,GAAGD,OAAO,CAAC,qBAAqB,CAAC;AAC3C,IAAIE,MAAM,GAAGF,OAAO,CAAC,+BAA+B,CAAC;AACrD,IAAIG,GAAG,GAAGH,OAAO,CAAC,kBAAkB,CAAC;AACrC,IAAII,aAAa,GAAGJ,OAAO,CAAC,2CAA2C,CAAC;AACxE,IAAIK,iBAAiB,GAAGL,OAAO,CAAC,gCAAgC,CAAC;AAEjE,IAAIM,MAAM,GAAGP,UAAU,CAACO,MAAM;AAC9B,IAAIC,qBAAqB,GAAGN,MAAM,CAAC,KAAK,CAAC;AACzC,IAAIO,qBAAqB,GAAGH,iBAAiB,GAAGC,MAAM,CAAC,KAAK,CAAC,IAAIA,MAAM,GAAGA,MAAM,IAAIA,MAAM,CAACG,aAAa,IAAIN,GAAG;AAE/GO,MAAM,CAACC,OAAO,GAAG,UAAUC,IAAI,EAAE;EAC/B,IAAI,CAACV,MAAM,CAACK,qBAAqB,EAAEK,IAAI,CAAC,EAAE;IACxCL,qBAAqB,CAACK,IAAI,CAAC,GAAGR,aAAa,IAAIF,MAAM,CAACI,MAAM,EAAEM,IAAI,CAAC,GAC/DN,MAAM,CAACM,IAAI,CAAC,GACZJ,qBAAqB,CAAC,SAAS,GAAGI,IAAI,CAAC;EAC7C;EAAE,OAAOL,qBAAqB,CAACK,IAAI,CAAC;AACtC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}