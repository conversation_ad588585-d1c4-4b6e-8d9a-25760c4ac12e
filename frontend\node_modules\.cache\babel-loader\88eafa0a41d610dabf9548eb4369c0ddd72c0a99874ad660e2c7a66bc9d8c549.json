{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Rajeev\\\\Code\\\\N8N-UI\\\\frontend\\\\src\\\\app.js\",\n  _s = $RefreshSig$();\n// Import React and hooks\nimport React, { useState, useEffect } from 'react';\n\n// Import Axios to make HTTP requests to the backend\nimport axios from 'axios';\n\n// Import the CSS file for styling\nimport './App.css';\n\n// Define the main App component\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  // State for workflows and UI\n  const [workflows, setWorkflows] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [selectedWorkflow, setSelectedWorkflow] = useState(null);\n  const [workflowParameters, setWorkflowParameters] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n  const [messageType, setMessageType] = useState(''); // 'success' or 'error'\n\n  // Load workflows and categories on component mount\n  useEffect(() => {\n    loadWorkflows();\n    loadCategories();\n  }, []);\n\n  // Function to load all workflows\n  const loadWorkflows = async () => {\n    try {\n      const response = await axios.get('http://localhost:3001/api/workflows');\n      setWorkflows(response.data.workflows);\n    } catch (error) {\n      console.error('Error loading workflows:', error);\n      showMessage('Failed to load workflows', 'error');\n    }\n  };\n\n  // Function to load categories\n  const loadCategories = async () => {\n    try {\n      const response = await axios.get('http://localhost:3001/api/workflows/categories');\n      setCategories(['all', ...response.data.categories]);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n    }\n  };\n\n  // Function to show messages\n  const showMessage = (text, type) => {\n    setMessage(text);\n    setMessageType(type);\n    setTimeout(() => {\n      setMessage('');\n      setMessageType('');\n    }, 5000);\n  };\n\n  // Function to filter workflows by category\n  const getFilteredWorkflows = () => {\n    if (selectedCategory === 'all') {\n      return workflows;\n    }\n    return workflows.filter(w => w.category === selectedCategory);\n  };\n\n  // Function to handle workflow selection\n  const selectWorkflow = workflow => {\n    setSelectedWorkflow(workflow);\n    // Initialize parameters with default values\n    const initialParams = {};\n    workflow.parameters.forEach(param => {\n      if (param.default !== undefined) {\n        initialParams[param.name] = param.default;\n      }\n    });\n    setWorkflowParameters(initialParams);\n  };\n\n  // Function to handle parameter changes\n  const handleParameterChange = (paramName, value) => {\n    setWorkflowParameters(prev => ({\n      ...prev,\n      [paramName]: value\n    }));\n  };\n\n  // Function to trigger a workflow\n  const triggerWorkflow = async () => {\n    if (!selectedWorkflow) return;\n    setLoading(true);\n    try {\n      const response = await axios.post(`http://localhost:3001/api/workflows/${selectedWorkflow.id}/invoke`, workflowParameters);\n      if (response.data.success) {\n        showMessage(response.data.message, 'success');\n      } else {\n        showMessage(response.data.error || 'Workflow execution failed', 'error');\n      }\n    } catch (error) {\n      var _error$response, _error$response$data, _error$response2, _error$response2$data;\n      console.error('Error triggering workflow:', error);\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.details) || ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.error) || 'Failed to trigger workflow';\n      showMessage(errorMessage, 'error');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Return the JSX to render the UI\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"app-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\uD83D\\uDE80 N8N Workflow Manager\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Trigger and manage your N8N workflows with ease\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `message ${messageType}`,\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"app-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"category-filter\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"category-select\",\n            children: \"Filter by category:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            id: \"category-select\",\n            value: selectedCategory,\n            onChange: e => setSelectedCategory(e.target.value),\n            children: categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: category,\n              children: category === 'all' ? 'All Categories' : category\n            }, category, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"workflow-list\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [\"Available Workflows (\", getFilteredWorkflows().length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), getFilteredWorkflows().map(workflow => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `workflow-item ${(selectedWorkflow === null || selectedWorkflow === void 0 ? void 0 : selectedWorkflow.id) === workflow.id ? 'selected' : ''}`,\n            onClick: () => selectWorkflow(workflow),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"workflow-icon\",\n              children: workflow.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"workflow-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: workflow.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: workflow.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"workflow-category\",\n                children: workflow.category\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 17\n            }, this)]\n          }, workflow.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"main-panel\",\n        children: selectedWorkflow ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"workflow-details\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"workflow-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: [selectedWorkflow.icon, \" \", selectedWorkflow.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: selectedWorkflow.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"workflow-meta\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"trigger-type\",\n                children: [\"Trigger: \", selectedWorkflow.triggerType]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"category\",\n                children: [\"Category: \", selectedWorkflow.category]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 15\n          }, this), selectedWorkflow.parameters.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"parameters-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Parameters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"parameters-form\",\n              children: selectedWorkflow.parameters.map(param => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"parameter-field\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: param.name,\n                  children: [param.name, param.required && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"required\",\n                    children: \"*\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 191,\n                    columnNumber: 46\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"parameter-description\",\n                  children: param.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 25\n                }, this), param.type === 'select' ? /*#__PURE__*/_jsxDEV(\"select\", {\n                  id: param.name,\n                  value: workflowParameters[param.name] || '',\n                  onChange: e => handleParameterChange(param.name, e.target.value),\n                  required: param.required,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select an option...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 203,\n                    columnNumber: 29\n                  }, this), param.options.map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: option,\n                    children: option\n                  }, option, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 205,\n                    columnNumber: 31\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 27\n                }, this) : param.type === 'text' ? /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  id: param.name,\n                  value: workflowParameters[param.name] || '',\n                  onChange: e => handleParameterChange(param.name, e.target.value),\n                  placeholder: param.description,\n                  required: param.required,\n                  rows: 3\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 27\n                }, this) : /*#__PURE__*/_jsxDEV(\"input\", {\n                  id: param.name,\n                  type: param.type === 'email' ? 'email' : param.type === 'url' ? 'url' : 'text',\n                  value: workflowParameters[param.name] || '',\n                  onChange: e => handleParameterChange(param.name, e.target.value),\n                  placeholder: param.description,\n                  required: param.required\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 27\n                }, this)]\n              }, param.name, true, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"trigger-section\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"trigger-button\",\n              onClick: triggerWorkflow,\n              disabled: loading,\n              children: loading ? 'Triggering...' : `🚀 Trigger ${selectedWorkflow.name}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-selection\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Select a Workflow\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Choose a workflow from the list to see its details and trigger it.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 117,\n    columnNumber: 5\n  }, this);\n}\n\n// Export the App component to be used in index.js\n_s(App, \"PhAtIpHQatPtyZzvUe06Es6ESCc=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "App", "_s", "workflows", "setWorkflows", "categories", "setCategories", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedWorkflow", "setSelectedWorkflow", "workflowParameters", "setWorkflowParameters", "loading", "setLoading", "message", "setMessage", "messageType", "setMessageType", "loadWorkflows", "loadCategories", "response", "get", "data", "error", "console", "showMessage", "text", "type", "setTimeout", "getFilteredWorkflows", "filter", "w", "category", "selectWorkflow", "workflow", "initialParams", "parameters", "for<PERSON>ach", "param", "default", "undefined", "name", "handleParameterChange", "paramName", "value", "prev", "triggerWorkflow", "post", "id", "success", "_error$response", "_error$response$data", "_error$response2", "_error$response2$data", "errorMessage", "details", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "htmlFor", "onChange", "e", "target", "map", "length", "onClick", "icon", "description", "triggerType", "required", "options", "option", "placeholder", "rows", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Rajeev/Code/N8N-UI/frontend/src/app.js"], "sourcesContent": ["// Import React and hooks\r\nimport React, { useState, useEffect } from 'react';\r\n\r\n// Import Axios to make HTTP requests to the backend\r\nimport axios from 'axios';\r\n\r\n// Import the CSS file for styling\r\nimport './App.css';\r\n\r\n// Define the main App component\r\nfunction App() {\r\n  // State for workflows and UI\r\n  const [workflows, setWorkflows] = useState([]);\r\n  const [categories, setCategories] = useState([]);\r\n  const [selectedCategory, setSelectedCategory] = useState('all');\r\n  const [selectedWorkflow, setSelectedWorkflow] = useState(null);\r\n  const [workflowParameters, setWorkflowParameters] = useState({});\r\n  const [loading, setLoading] = useState(false);\r\n  const [message, setMessage] = useState('');\r\n  const [messageType, setMessageType] = useState(''); // 'success' or 'error'\r\n\r\n  // Load workflows and categories on component mount\r\n  useEffect(() => {\r\n    loadWorkflows();\r\n    loadCategories();\r\n  }, []);\r\n\r\n  // Function to load all workflows\r\n  const loadWorkflows = async () => {\r\n    try {\r\n      const response = await axios.get('http://localhost:3001/api/workflows');\r\n      setWorkflows(response.data.workflows);\r\n    } catch (error) {\r\n      console.error('Error loading workflows:', error);\r\n      showMessage('Failed to load workflows', 'error');\r\n    }\r\n  };\r\n\r\n  // Function to load categories\r\n  const loadCategories = async () => {\r\n    try {\r\n      const response = await axios.get('http://localhost:3001/api/workflows/categories');\r\n      setCategories(['all', ...response.data.categories]);\r\n    } catch (error) {\r\n      console.error('Error loading categories:', error);\r\n    }\r\n  };\r\n\r\n  // Function to show messages\r\n  const showMessage = (text, type) => {\r\n    setMessage(text);\r\n    setMessageType(type);\r\n    setTimeout(() => {\r\n      setMessage('');\r\n      setMessageType('');\r\n    }, 5000);\r\n  };\r\n\r\n  // Function to filter workflows by category\r\n  const getFilteredWorkflows = () => {\r\n    if (selectedCategory === 'all') {\r\n      return workflows;\r\n    }\r\n    return workflows.filter(w => w.category === selectedCategory);\r\n  };\r\n\r\n  // Function to handle workflow selection\r\n  const selectWorkflow = (workflow) => {\r\n    setSelectedWorkflow(workflow);\r\n    // Initialize parameters with default values\r\n    const initialParams = {};\r\n    workflow.parameters.forEach(param => {\r\n      if (param.default !== undefined) {\r\n        initialParams[param.name] = param.default;\r\n      }\r\n    });\r\n    setWorkflowParameters(initialParams);\r\n  };\r\n\r\n  // Function to handle parameter changes\r\n  const handleParameterChange = (paramName, value) => {\r\n    setWorkflowParameters(prev => ({\r\n      ...prev,\r\n      [paramName]: value\r\n    }));\r\n  };\r\n\r\n  // Function to trigger a workflow\r\n  const triggerWorkflow = async () => {\r\n    if (!selectedWorkflow) return;\r\n\r\n    setLoading(true);\r\n    try {\r\n      const response = await axios.post(\r\n        `http://localhost:3001/api/workflows/${selectedWorkflow.id}/invoke`,\r\n        workflowParameters\r\n      );\r\n\r\n      if (response.data.success) {\r\n        showMessage(response.data.message, 'success');\r\n      } else {\r\n        showMessage(response.data.error || 'Workflow execution failed', 'error');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error triggering workflow:', error);\r\n      const errorMessage = error.response?.data?.details ||\r\n                          error.response?.data?.error ||\r\n                          'Failed to trigger workflow';\r\n      showMessage(errorMessage, 'error');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Return the JSX to render the UI\r\n  return (\r\n    <div className=\"App\">\r\n      {/* Header */}\r\n      <header className=\"app-header\">\r\n        <h1>🚀 N8N Workflow Manager</h1>\r\n        <p>Trigger and manage your N8N workflows with ease</p>\r\n      </header>\r\n\r\n      {/* Message display */}\r\n      {message && (\r\n        <div className={`message ${messageType}`}>\r\n          {message}\r\n        </div>\r\n      )}\r\n\r\n      {/* Main content */}\r\n      <div className=\"app-content\">\r\n        {/* Sidebar with workflow list */}\r\n        <div className=\"sidebar\">\r\n          <div className=\"category-filter\">\r\n            <label htmlFor=\"category-select\">Filter by category:</label>\r\n            <select\r\n              id=\"category-select\"\r\n              value={selectedCategory}\r\n              onChange={(e) => setSelectedCategory(e.target.value)}\r\n            >\r\n              {categories.map(category => (\r\n                <option key={category} value={category}>\r\n                  {category === 'all' ? 'All Categories' : category}\r\n                </option>\r\n              ))}\r\n            </select>\r\n          </div>\r\n\r\n          <div className=\"workflow-list\">\r\n            <h3>Available Workflows ({getFilteredWorkflows().length})</h3>\r\n            {getFilteredWorkflows().map(workflow => (\r\n              <div\r\n                key={workflow.id}\r\n                className={`workflow-item ${selectedWorkflow?.id === workflow.id ? 'selected' : ''}`}\r\n                onClick={() => selectWorkflow(workflow)}\r\n              >\r\n                <div className=\"workflow-icon\">{workflow.icon}</div>\r\n                <div className=\"workflow-info\">\r\n                  <h4>{workflow.name}</h4>\r\n                  <p>{workflow.description}</p>\r\n                  <span className=\"workflow-category\">{workflow.category}</span>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Main panel with workflow details and parameters */}\r\n        <div className=\"main-panel\">\r\n          {selectedWorkflow ? (\r\n            <div className=\"workflow-details\">\r\n              <div className=\"workflow-header\">\r\n                <h2>{selectedWorkflow.icon} {selectedWorkflow.name}</h2>\r\n                <p>{selectedWorkflow.description}</p>\r\n                <div className=\"workflow-meta\">\r\n                  <span className=\"trigger-type\">Trigger: {selectedWorkflow.triggerType}</span>\r\n                  <span className=\"category\">Category: {selectedWorkflow.category}</span>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Parameters form */}\r\n              {selectedWorkflow.parameters.length > 0 && (\r\n                <div className=\"parameters-section\">\r\n                  <h3>Parameters</h3>\r\n                  <div className=\"parameters-form\">\r\n                    {selectedWorkflow.parameters.map(param => (\r\n                      <div key={param.name} className=\"parameter-field\">\r\n                        <label htmlFor={param.name}>\r\n                          {param.name}\r\n                          {param.required && <span className=\"required\">*</span>}\r\n                        </label>\r\n                        <p className=\"parameter-description\">{param.description}</p>\r\n\r\n                        {/* Render different input types based on parameter type */}\r\n                        {param.type === 'select' ? (\r\n                          <select\r\n                            id={param.name}\r\n                            value={workflowParameters[param.name] || ''}\r\n                            onChange={(e) => handleParameterChange(param.name, e.target.value)}\r\n                            required={param.required}\r\n                          >\r\n                            <option value=\"\">Select an option...</option>\r\n                            {param.options.map(option => (\r\n                              <option key={option} value={option}>{option}</option>\r\n                            ))}\r\n                          </select>\r\n                        ) : param.type === 'text' ? (\r\n                          <textarea\r\n                            id={param.name}\r\n                            value={workflowParameters[param.name] || ''}\r\n                            onChange={(e) => handleParameterChange(param.name, e.target.value)}\r\n                            placeholder={param.description}\r\n                            required={param.required}\r\n                            rows={3}\r\n                          />\r\n                        ) : (\r\n                          <input\r\n                            id={param.name}\r\n                            type={param.type === 'email' ? 'email' : param.type === 'url' ? 'url' : 'text'}\r\n                            value={workflowParameters[param.name] || ''}\r\n                            onChange={(e) => handleParameterChange(param.name, e.target.value)}\r\n                            placeholder={param.description}\r\n                            required={param.required}\r\n                          />\r\n                        )}\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Trigger button */}\r\n              <div className=\"trigger-section\">\r\n                <button\r\n                  className=\"trigger-button\"\r\n                  onClick={triggerWorkflow}\r\n                  disabled={loading}\r\n                >\r\n                  {loading ? 'Triggering...' : `🚀 Trigger ${selectedWorkflow.name}`}\r\n                </button>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"no-selection\">\r\n              <h2>Select a Workflow</h2>\r\n              <p>Choose a workflow from the list to see its details and trigger it.</p>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\n// Export the App component to be used in index.js\r\nexport default App;"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;;AAElD;AACA,OAAOC,KAAK,MAAM,OAAO;;AAEzB;AACA,OAAO,WAAW;;AAElB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACS,UAAU,EAAEC,aAAa,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACW,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACa,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACe,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChE,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;EAEpD;EACAC,SAAS,CAAC,MAAM;IACdsB,aAAa,CAAC,CAAC;IACfC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMD,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMvB,KAAK,CAACwB,GAAG,CAAC,qCAAqC,CAAC;MACvElB,YAAY,CAACiB,QAAQ,CAACE,IAAI,CAACpB,SAAS,CAAC;IACvC,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDE,WAAW,CAAC,0BAA0B,EAAE,OAAO,CAAC;IAClD;EACF,CAAC;;EAED;EACA,MAAMN,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMvB,KAAK,CAACwB,GAAG,CAAC,gDAAgD,CAAC;MAClFhB,aAAa,CAAC,CAAC,KAAK,EAAE,GAAGe,QAAQ,CAACE,IAAI,CAAClB,UAAU,CAAC,CAAC;IACrD,CAAC,CAAC,OAAOmB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD;EACF,CAAC;;EAED;EACA,MAAME,WAAW,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK;IAClCZ,UAAU,CAACW,IAAI,CAAC;IAChBT,cAAc,CAACU,IAAI,CAAC;IACpBC,UAAU,CAAC,MAAM;MACfb,UAAU,CAAC,EAAE,CAAC;MACdE,cAAc,CAAC,EAAE,CAAC;IACpB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;;EAED;EACA,MAAMY,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAIvB,gBAAgB,KAAK,KAAK,EAAE;MAC9B,OAAOJ,SAAS;IAClB;IACA,OAAOA,SAAS,CAAC4B,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,KAAK1B,gBAAgB,CAAC;EAC/D,CAAC;;EAED;EACA,MAAM2B,cAAc,GAAIC,QAAQ,IAAK;IACnCzB,mBAAmB,CAACyB,QAAQ,CAAC;IAC7B;IACA,MAAMC,aAAa,GAAG,CAAC,CAAC;IACxBD,QAAQ,CAACE,UAAU,CAACC,OAAO,CAACC,KAAK,IAAI;MACnC,IAAIA,KAAK,CAACC,OAAO,KAAKC,SAAS,EAAE;QAC/BL,aAAa,CAACG,KAAK,CAACG,IAAI,CAAC,GAAGH,KAAK,CAACC,OAAO;MAC3C;IACF,CAAC,CAAC;IACF5B,qBAAqB,CAACwB,aAAa,CAAC;EACtC,CAAC;;EAED;EACA,MAAMO,qBAAqB,GAAGA,CAACC,SAAS,EAAEC,KAAK,KAAK;IAClDjC,qBAAqB,CAACkC,IAAI,KAAK;MAC7B,GAAGA,IAAI;MACP,CAACF,SAAS,GAAGC;IACf,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAME,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAACtC,gBAAgB,EAAE;IAEvBK,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMO,QAAQ,GAAG,MAAMvB,KAAK,CAACkD,IAAI,CAC/B,uCAAuCvC,gBAAgB,CAACwC,EAAE,SAAS,EACnEtC,kBACF,CAAC;MAED,IAAIU,QAAQ,CAACE,IAAI,CAAC2B,OAAO,EAAE;QACzBxB,WAAW,CAACL,QAAQ,CAACE,IAAI,CAACR,OAAO,EAAE,SAAS,CAAC;MAC/C,CAAC,MAAM;QACLW,WAAW,CAACL,QAAQ,CAACE,IAAI,CAACC,KAAK,IAAI,2BAA2B,EAAE,OAAO,CAAC;MAC1E;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MAAA,IAAA2B,eAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACd7B,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAM+B,YAAY,GAAG,EAAAJ,eAAA,GAAA3B,KAAK,CAACH,QAAQ,cAAA8B,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB5B,IAAI,cAAA6B,oBAAA,uBAApBA,oBAAA,CAAsBI,OAAO,OAAAH,gBAAA,GAC9B7B,KAAK,CAACH,QAAQ,cAAAgC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB9B,IAAI,cAAA+B,qBAAA,uBAApBA,qBAAA,CAAsB9B,KAAK,KAC3B,4BAA4B;MAChDE,WAAW,CAAC6B,YAAY,EAAE,OAAO,CAAC;IACpC,CAAC,SAAS;MACRzC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,oBACEd,OAAA;IAAKyD,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAElB1D,OAAA;MAAQyD,SAAS,EAAC,YAAY;MAAAC,QAAA,gBAC5B1D,OAAA;QAAA0D,QAAA,EAAI;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChC9D,OAAA;QAAA0D,QAAA,EAAG;MAA+C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC,EAGR/C,OAAO,iBACNf,OAAA;MAAKyD,SAAS,EAAE,WAAWxC,WAAW,EAAG;MAAAyC,QAAA,EACtC3C;IAAO;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,eAGD9D,OAAA;MAAKyD,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAE1B1D,OAAA;QAAKyD,SAAS,EAAC,SAAS;QAAAC,QAAA,gBACtB1D,OAAA;UAAKyD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B1D,OAAA;YAAO+D,OAAO,EAAC,iBAAiB;YAAAL,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5D9D,OAAA;YACEiD,EAAE,EAAC,iBAAiB;YACpBJ,KAAK,EAAEtC,gBAAiB;YACxByD,QAAQ,EAAGC,CAAC,IAAKzD,mBAAmB,CAACyD,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;YAAAa,QAAA,EAEpDrD,UAAU,CAAC8D,GAAG,CAAClC,QAAQ,iBACtBjC,OAAA;cAAuB6C,KAAK,EAAEZ,QAAS;cAAAyB,QAAA,EACpCzB,QAAQ,KAAK,KAAK,GAAG,gBAAgB,GAAGA;YAAQ,GADtCA,QAAQ;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEb,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN9D,OAAA;UAAKyD,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B1D,OAAA;YAAA0D,QAAA,GAAI,uBAAqB,EAAC5B,oBAAoB,CAAC,CAAC,CAACsC,MAAM,EAAC,GAAC;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAC7DhC,oBAAoB,CAAC,CAAC,CAACqC,GAAG,CAAChC,QAAQ,iBAClCnC,OAAA;YAEEyD,SAAS,EAAE,iBAAiB,CAAAhD,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEwC,EAAE,MAAKd,QAAQ,CAACc,EAAE,GAAG,UAAU,GAAG,EAAE,EAAG;YACrFoB,OAAO,EAAEA,CAAA,KAAMnC,cAAc,CAACC,QAAQ,CAAE;YAAAuB,QAAA,gBAExC1D,OAAA;cAAKyD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEvB,QAAQ,CAACmC;YAAI;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpD9D,OAAA;cAAKyD,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B1D,OAAA;gBAAA0D,QAAA,EAAKvB,QAAQ,CAACO;cAAI;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxB9D,OAAA;gBAAA0D,QAAA,EAAIvB,QAAQ,CAACoC;cAAW;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7B9D,OAAA;gBAAMyD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAEvB,QAAQ,CAACF;cAAQ;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA,GATD3B,QAAQ,CAACc,EAAE;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUb,CACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN9D,OAAA;QAAKyD,SAAS,EAAC,YAAY;QAAAC,QAAA,EACxBjD,gBAAgB,gBACfT,OAAA;UAAKyD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/B1D,OAAA;YAAKyD,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9B1D,OAAA;cAAA0D,QAAA,GAAKjD,gBAAgB,CAAC6D,IAAI,EAAC,GAAC,EAAC7D,gBAAgB,CAACiC,IAAI;YAAA;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxD9D,OAAA;cAAA0D,QAAA,EAAIjD,gBAAgB,CAAC8D;YAAW;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrC9D,OAAA;cAAKyD,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B1D,OAAA;gBAAMyD,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAAC,WAAS,EAACjD,gBAAgB,CAAC+D,WAAW;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7E9D,OAAA;gBAAMyD,SAAS,EAAC,UAAU;gBAAAC,QAAA,GAAC,YAAU,EAACjD,gBAAgB,CAACwB,QAAQ;cAAA;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLrD,gBAAgB,CAAC4B,UAAU,CAAC+B,MAAM,GAAG,CAAC,iBACrCpE,OAAA;YAAKyD,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjC1D,OAAA;cAAA0D,QAAA,EAAI;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnB9D,OAAA;cAAKyD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC7BjD,gBAAgB,CAAC4B,UAAU,CAAC8B,GAAG,CAAC5B,KAAK,iBACpCvC,OAAA;gBAAsByD,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC/C1D,OAAA;kBAAO+D,OAAO,EAAExB,KAAK,CAACG,IAAK;kBAAAgB,QAAA,GACxBnB,KAAK,CAACG,IAAI,EACVH,KAAK,CAACkC,QAAQ,iBAAIzE,OAAA;oBAAMyD,SAAS,EAAC,UAAU;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC,eACR9D,OAAA;kBAAGyD,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEnB,KAAK,CAACgC;gBAAW;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAG3DvB,KAAK,CAACX,IAAI,KAAK,QAAQ,gBACtB5B,OAAA;kBACEiD,EAAE,EAAEV,KAAK,CAACG,IAAK;kBACfG,KAAK,EAAElC,kBAAkB,CAAC4B,KAAK,CAACG,IAAI,CAAC,IAAI,EAAG;kBAC5CsB,QAAQ,EAAGC,CAAC,IAAKtB,qBAAqB,CAACJ,KAAK,CAACG,IAAI,EAAEuB,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;kBACnE4B,QAAQ,EAAElC,KAAK,CAACkC,QAAS;kBAAAf,QAAA,gBAEzB1D,OAAA;oBAAQ6C,KAAK,EAAC,EAAE;oBAAAa,QAAA,EAAC;kBAAmB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAC5CvB,KAAK,CAACmC,OAAO,CAACP,GAAG,CAACQ,MAAM,iBACvB3E,OAAA;oBAAqB6C,KAAK,EAAE8B,MAAO;oBAAAjB,QAAA,EAAEiB;kBAAM,GAA9BA,MAAM;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAiC,CACrD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,GACPvB,KAAK,CAACX,IAAI,KAAK,MAAM,gBACvB5B,OAAA;kBACEiD,EAAE,EAAEV,KAAK,CAACG,IAAK;kBACfG,KAAK,EAAElC,kBAAkB,CAAC4B,KAAK,CAACG,IAAI,CAAC,IAAI,EAAG;kBAC5CsB,QAAQ,EAAGC,CAAC,IAAKtB,qBAAqB,CAACJ,KAAK,CAACG,IAAI,EAAEuB,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;kBACnE+B,WAAW,EAAErC,KAAK,CAACgC,WAAY;kBAC/BE,QAAQ,EAAElC,KAAK,CAACkC,QAAS;kBACzBI,IAAI,EAAE;gBAAE;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,gBAEF9D,OAAA;kBACEiD,EAAE,EAAEV,KAAK,CAACG,IAAK;kBACfd,IAAI,EAAEW,KAAK,CAACX,IAAI,KAAK,OAAO,GAAG,OAAO,GAAGW,KAAK,CAACX,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG,MAAO;kBAC/EiB,KAAK,EAAElC,kBAAkB,CAAC4B,KAAK,CAACG,IAAI,CAAC,IAAI,EAAG;kBAC5CsB,QAAQ,EAAGC,CAAC,IAAKtB,qBAAqB,CAACJ,KAAK,CAACG,IAAI,EAAEuB,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;kBACnE+B,WAAW,EAAErC,KAAK,CAACgC,WAAY;kBAC/BE,QAAQ,EAAElC,KAAK,CAACkC;gBAAS;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CACF;cAAA,GAtCOvB,KAAK,CAACG,IAAI;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAuCf,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGD9D,OAAA;YAAKyD,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9B1D,OAAA;cACEyD,SAAS,EAAC,gBAAgB;cAC1BY,OAAO,EAAEtB,eAAgB;cACzB+B,QAAQ,EAAEjE,OAAQ;cAAA6C,QAAA,EAEjB7C,OAAO,GAAG,eAAe,GAAG,cAAcJ,gBAAgB,CAACiC,IAAI;YAAE;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEN9D,OAAA;UAAKyD,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B1D,OAAA;YAAA0D,QAAA,EAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1B9D,OAAA;YAAA0D,QAAA,EAAG;UAAkE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;;AAEA;AAAA5D,EAAA,CArPSD,GAAG;AAAA8E,EAAA,GAAH9E,GAAG;AAsPZ,eAAeA,GAAG;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}