// Import the Express framework to create the server
const express = require('express');

// Import Axios to make HTTP requests to n8n
const axios = require('axios');

// Import CORS to allow cross-origin requests from the frontend
const cors = require('cors');

// Create an instance of the Express application
const app = express();

// Define the port number for the backend server
const PORT = 3001;

// Enable CORS so the frontend can communicate with the backend
app.use(cors());

// Enable JSON body parsing for incoming requests
app.use(express.json());

// Define a simple GET route for the root path
app.get('/', (req, res) => {
  res.json({
    message: 'N8N Backend API is running!',
    endpoints: {
      'POST /api/workflows/:id/invoke': 'Trigger a workflow by ID'
    }
  });
});

// Define a POST route to trigger a workflow by ID
app.post('/api/workflows/:id/invoke', async (req, res) => {
  try {
    // Extract the workflow ID from the request parameters
    const workflowId = req.params.id;

    // Define the webhook URL for the n8n workflow
    // Replace 'test-webhook' with your actual webhook path
    const webhookUrl = `http://localhost:5678/webhook/test-webhook`;

    // Send a POST request to the n8n webhook to trigger the workflow
    await axios.post(webhookUrl);

    // Send a success response to the frontend
    res.json({ message: 'Workflow triggered successfully!' });
  } catch (error) {
    // Log the error to the console for debugging
    console.error('Error triggering workflow:', error.message);

    // Send an error response to the frontend
    res.status(500).json({ error: 'Failed to trigger workflow' });
  }
});

// Start the server and listen on the specified port
app.listen(PORT, () => {
  // Log a message to confirm the server is running
  console.log(`🚀 Backend server started successfully!`);
  console.log(`📍 Server URL: http://localhost:${PORT}`);
  console.log(`🔗 Click here: http://localhost:${PORT}`);
  console.log(`📋 Available endpoints:`);
  console.log(`   POST http://localhost:${PORT}/api/workflows/:id/invoke`);
});
