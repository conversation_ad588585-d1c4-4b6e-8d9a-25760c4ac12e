[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\Rajeev\\Code\\N8N-UI\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Rajeev\\Code\\N8N-UI\\frontend\\src\\app.js": "2"}, {"size": 470, "mtime": 1752094702096, "results": "3", "hashOfConfig": "4"}, {"size": 9526, "mtime": 1752099025278, "results": "5", "hashOfConfig": "4"}, {"filePath": "6", "messages": "7", "suppressedMessages": "8", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "15wjy7l", {"filePath": "9", "messages": "10", "suppressedMessages": "11", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\Rajeev\\Code\\N8N-UI\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Rajeev\\Code\\N8N-UI\\frontend\\src\\app.js", ["12"], [], {"ruleId": "13", "severity": 1, "message": "14", "line": 26, "column": 6, "nodeType": "15", "endLine": 26, "endColumn": 8, "suggestions": "16"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadWorkflows'. Either include it or remove the dependency array.", "ArrayExpression", ["17"], {"desc": "18", "fix": "19"}, "Update the dependencies array to be: [loadWorkflows]", {"range": "20", "text": "21"}, [922, 924], "[loadWorkflows]"]